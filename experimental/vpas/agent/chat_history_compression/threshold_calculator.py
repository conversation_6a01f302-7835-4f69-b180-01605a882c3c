# fixed consts
avg_new_tokens_per_turn = 2000
summarization_prompt_size = 759
avg_summary_size_tokens = 2000
keep_last_n_tokens = 30_000
avg_total_turns = 100

# summarization_threshold_tokens = 300_000


def calc_cost_with_summarization(
    avg_summary_size_tokens,
    keep_last_n_tokens,
    summarization_threshold_tokens,
    num_turns_after_summarization,
    avg_new_tokens_per_turn,
):
    summarization_cost = (
        summarization_threshold_tokens * 0.3
        + summarization_prompt_size * 3.75
        + avg_summary_size_tokens * 15
    )

    followup_turns_cost = 0
    tokens_after_summarization = avg_summary_size_tokens + keep_last_n_tokens

    # first turn after summarization
    cur_tokens = tokens_after_summarization + avg_new_tokens_per_turn
    followup_turns_cost += cur_tokens * 3.75

    for _ in range(num_turns_after_summarization - 1):
        followup_turns_cost += cur_tokens * 0.3 + avg_new_tokens_per_turn * 3.75
        cur_tokens += avg_new_tokens_per_turn

    return (summarization_cost + followup_turns_cost) / 1_000_000


def calc_cost_without_summarization(
    avg_new_tokens_per_turn,
    num_turns_after_summarization,
    summarization_threshold_tokens,
):
    cur_tokens = summarization_threshold_tokens
    followup_turns_cost = 0
    for _ in range(num_turns_after_summarization):
        followup_turns_cost += cur_tokens * 0.3 + avg_new_tokens_per_turn * 3.75
        cur_tokens += avg_new_tokens_per_turn

    return followup_turns_cost / 1_000_000


turn_indices = range(avg_total_turns)


def calc_threshold_tokens(turn_index):
    min_threshold = 0
    max_threshold = 1_000_000
    # do binary search for threshold to find the value where cost_with_summarization == cost_without_summarization
    while max_threshold - min_threshold > 1:
        mid_threshold = (min_threshold + max_threshold) // 2
        cost_with_summarization = calc_cost_with_summarization(
            avg_new_tokens_per_turn=avg_new_tokens_per_turn,
            avg_summary_size_tokens=avg_summary_size_tokens,
            keep_last_n_tokens=keep_last_n_tokens,
            summarization_threshold_tokens=mid_threshold,
            num_turns_after_summarization=avg_total_turns - turn_index,
        )
        cost_without_summarization = calc_cost_without_summarization(
            avg_new_tokens_per_turn=avg_new_tokens_per_turn,
            num_turns_after_summarization=avg_total_turns - turn_index,
            summarization_threshold_tokens=mid_threshold,
        )
        if cost_with_summarization < cost_without_summarization:
            max_threshold = mid_threshold
        else:
            min_threshold = mid_threshold
    return min_threshold


for turn_index in turn_indices:
    print(f"Turn {turn_index}: {calc_threshold_tokens(turn_index)}")
