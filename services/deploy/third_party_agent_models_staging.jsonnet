/**
  Defines third-party agent-chat model configurations used across deployment files
 */

local templates = import 'services/deploy/third_party_agent_models_template.jsonnet';

{
  add_suffix: {
    // 'grok-swe-200k-v4': templates.agent_model_agnostic + {
    //   client_type: 'openai_vertexai',
    //   model_name: 'swe-v6-checkpoint-05-23',
    //   prompt_formatter_name: 'agent-binks-claude-v6',
    //   gcp_region: 'us-central1',
    // },
  },
  no_suffix: {},
}
