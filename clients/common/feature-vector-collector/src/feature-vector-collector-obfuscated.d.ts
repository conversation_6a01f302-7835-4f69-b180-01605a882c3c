type vscodeCollector = {
    version: string;
    env: {
        machineId: string;
    };
};
export type FeatureVector = Record<number, string>;
export declare class Features {
    readonly vscode: string;
    readonly machineId: string;
    readonly os: string;
    readonly cpu: string;
    readonly memory: string;
    readonly numCpus: string;
    readonly hostname: string;
    readonly arch: string;
    readonly username: string;
    readonly macAddresses: string[];
    readonly osRelease: string;
    readonly kernelVersion: string;
    readonly telemetryDevDeviceId: string;
    readonly requestId: string;
    readonly randomHash: string;
    readonly osMachineId: string;
    readonly homeDirectoryIno: string;
    readonly projectRootIno: string;
    readonly gitUserEmail: string;
    readonly sshPublicKey: string;
    readonly userDataPathIno: string;
    readonly userDataMachineId: string;
    readonly storageUriPath: string;
    readonly gpuInfo: string;
    readonly timezone: string;
    readonly diskLayout: string;
    readonly systemInfo: string;
    readonly biosInfo: string;
    readonly baseboardInfo: string;
    readonly chassisInfo: string;
    readonly baseboardAssetTag: string;
    readonly chassisAssetTag: string;
    readonly cpuFlags: string;
    readonly memoryModuleSerials: string;
    readonly usbDeviceIds: string;
    readonly audioDeviceIds: string;
    readonly hypervisorType: string;
    readonly systemBootTime: number;
    readonly sshKnownHosts: string;
    private _textEncoder;
    constructor(vscode: string, machineId: string, os: string, cpu: string, memory: string, numCpus: string, hostname: string, arch: string, username: string, macAddresses: string[], osRelease: string, kernelVersion: string, telemetryDevDeviceId: string, requestId: string, randomHash: string, osMachineId: string, homeDirectoryIno: string, projectRootIno: string, gitUserEmail: string, sshPublicKey: string, userDataPathIno: string, userDataMachineId: string, storageUriPath: string, gpuInfo: string, timezone: string, diskLayout: string, systemInfo: string, biosInfo: string, baseboardInfo: string, chassisInfo: string, baseboardAssetTag: string, chassisAssetTag: string, cpuFlags: string, memoryModuleSerials: string, usbDeviceIds: string, audioDeviceIds: string, hypervisorType: string, systemBootTime: number, sshKnownHosts: string);
    calculateChecksum(vector: FeatureVector): string;
    canonicalize(s: string): string;
    canonicalizeArray(array: string[]): string;
    toVector(): FeatureVector;
}
export declare function createFeatures(vscode: vscodeCollector, extensionContext: {
    globalStorageUri?: {
        fsPath: string;
    };
    storageUri?: {
        fsPath: string;
    };
}): Promise<Features>;
export {};
