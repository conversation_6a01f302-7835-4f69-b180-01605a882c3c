/**
 * @file task-tools-utils.ts
 * Utility functions for task tools that don't require instance state.
 * These functions handle validation and data transformation for task operations.
 */

import { TaskState } from "../../agent/task/task-types";

/**
 * Type representing validated task input with proper typing
 */
export interface ValidatedTaskInput {
  name: string;
  description: string;
  parentTaskId: string | undefined;
  afterTaskId: string | undefined;
}

/**
 * Type representing validated task update input with proper typing
 */
export interface ValidatedTaskUpdateInput {
  taskId: string;
  state: TaskState | undefined;
  name: string | undefined;
  description: string | undefined;
}

/**
 * Validates task update input and returns typed result for TypeScript narrowing.
 *
 * @param taskInput - Raw input from tool call
 * @returns Validated input with trimmed strings and validated state
 * @throws Error if task_id missing, state invalid, or any field has wrong type
 */
export function validateTaskUpdateInput(
  taskInput: Record<string, unknown>,
): ValidatedTaskUpdateInput {
  const taskId = taskInput.task_id;

  // Task ID is required
  if (!taskId || typeof taskId !== "string" || taskId.trim() === "") {
    throw new Error("Task ID is required and must be a non-empty string.");
  }

  // Validate state if provided
  let state: TaskState | undefined;
  if (taskInput.state !== undefined) {
    const stateValue = taskInput.state;
    if (typeof stateValue !== "string") {
      throw new Error("State must be a string if provided.");
    }
    if (!Object.values(TaskState).includes(stateValue as TaskState)) {
      throw new Error(
        `Invalid state: ${stateValue}. Must be one of: NOT_STARTED, IN_PROGRESS, CANCELLED, COMPLETE.`,
      );
    }
    state = stateValue as TaskState;
  }

  // Validate name if provided
  let name: string | undefined;
  if (taskInput.name !== undefined) {
    if (typeof taskInput.name !== "string") {
      throw new Error("Name must be a string if provided.");
    }
    name = taskInput.name.trim() || undefined; // Convert empty string to undefined
  }

  // Validate description if provided
  let description: string | undefined;
  if (taskInput.description !== undefined) {
    if (typeof taskInput.description !== "string") {
      throw new Error("Description must be a string if provided.");
    }
    description = taskInput.description.trim() || undefined; // Convert empty string to undefined
  }

  return {
    taskId: taskId.trim(),
    state,
    name,
    description,
  };
}

/**
 * Builds update object from validated input, filtering out undefined values.
 *
 * @param validatedInput - Validated task update input
 * @returns Update object with only defined properties
 * @throws Error if no properties to update
 */
export function buildUpdateObject(
  validatedInput: ValidatedTaskUpdateInput,
): Partial<{ state: TaskState; name: string; description: string }> {
  const updates: Partial<{
    state: TaskState;
    name: string;
    description: string;
  }> = {};

  if (validatedInput.state !== undefined) {
    updates.state = validatedInput.state;
  }
  if (validatedInput.name !== undefined) {
    updates.name = validatedInput.name;
  }
  if (validatedInput.description !== undefined) {
    updates.description = validatedInput.description;
  }

  if (Object.keys(updates).length === 0) {
    throw new Error(
      "At least one property (state, name, description) must be provided to update.",
    );
  }

  return updates;
}

/**
 * Validates task input and returns typed result for TypeScript narrowing.
 *
 * @param taskInput - Raw input from tool call
 * @returns Validated input with trimmed strings
 * @throws Error if name/description missing or any field has wrong type
 */
export function validateTaskInput(
  taskInput: Record<string, unknown>,
): ValidatedTaskInput {
  const name = taskInput.name;
  const description = taskInput.description;

  // Comprehensive type checking
  if (!name || typeof name !== "string" || name.trim() === "") {
    throw new Error("Name is required and must be a non-empty string.");
  }

  if (
    !description ||
    typeof description !== "string" ||
    description.trim() === ""
  ) {
    throw new Error("Description is required and must be a non-empty string.");
  }

  const parentTaskId = taskInput.parent_task_id;
  if (
    parentTaskId !== undefined &&
    (typeof parentTaskId !== "string" || parentTaskId.trim() === "")
  ) {
    throw new Error("Parent task ID must be a non-empty string if provided.");
  }

  const afterTaskId = taskInput.after_task_id;
  if (
    afterTaskId !== undefined &&
    (typeof afterTaskId !== "string" || afterTaskId.trim() === "")
  ) {
    throw new Error("After task ID must be a non-empty string if provided.");
  }

  return {
    name: name.trim(),
    description: description.trim(),
    parentTaskId: parentTaskId?.trim() || undefined,
    afterTaskId: afterTaskId?.trim() || undefined,
  };
}

/**
 * Validates task state, defaults to NOT_STARTED if not provided.
 *
 * @param taskInput - Input containing optional state field
 * @returns Valid TaskState enum value
 * @throws Error if state is invalid
 */
export function validateTaskState(
  taskInput: Record<string, unknown>,
): TaskState {
  const state = taskInput.state;
  const taskState = (state as TaskState) || TaskState.NOT_STARTED;
  if (state && !Object.values(TaskState).includes(taskState)) {
    throw new Error(
      `Invalid state: ${String(state)}. Must be one of: NOT_STARTED, IN_PROGRESS, CANCELLED, COMPLETE.`,
    );
  }
  return taskState;
}
